{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", "**/.env"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**", ".venv/**"], "cache": true}, "dev": {"persistent": true, "cache": false}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"], "cache": false}, "lint": {"outputs": [], "cache": true}, "typecheck": {"dependsOn": ["^build"], "outputs": [], "cache": true}, "clean": {"cache": false}}}