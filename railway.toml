# Railway configuration for DataHero4 Monorepo
# This file configures deployment for both backend and frontend services

# Backend Service Configuration
[build]
builder = "dockerfile"
dockerfilePath = "./Dockerfile"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 30
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 10
rootDirectory = "apps/backend"

# Frontend Service Configuration
[environments.production.frontend]
builder = "nixpacks"
buildCommand = "npm ci --only=production && npm run build"
startCommand = "npm run preview -- --port $PORT --host 0.0.0.0"
healthcheckPath = "/"
healthcheckTimeout = 30
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 10
rootDirectory = "apps/frontend"

# Environment variables for production
[environments.production]
variables = {
  NODE_ENV = "production",
  LOG_LEVEL = "INFO",
  USE_OPTIMIZED_WORKFLOW = "true",
  PYTHONUNBUFFERED = "1",
  PORT = "8000"
}

# Deploy configuration
[environments.production.deploy]
autoDeployEnabled = true
healthcheckGracePeriod = 30
