import React, { useState, useEffect } from 'react';
import { getDashboardSnapshot, convertSnapshotToKpiData } from '@/lib/api';

const TestSnapshot = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [kpis, setKpis] = useState<any[]>([]);
  const [rawSnapshot, setRawSnapshot] = useState<any>(null);

  useEffect(() => {
    const testSnapshot = async () => {
      try {
        console.log('🧪 Testing snapshot API...');
        const snapshot = await getDashboardSnapshot();
        console.log('🧪 Raw snapshot:', snapshot);
        setRawSnapshot(snapshot);

        if (snapshot.success && Object.keys(snapshot.data).length > 0) {
          const convertedKpis = convertSnapshotToKpiData(snapshot);
          console.log('🧪 Converted KPIs:', convertedKpis);
          setKpis(convertedKpis);
        } else {
          setError('Snapshot is empty or failed');
        }
      } catch (err) {
        console.error('🧪 Snapshot test failed:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    testSnapshot();
  }, []);

  if (loading) {
    return <div className="p-8">Loading snapshot test...</div>;
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Snapshot Test Failed</h1>
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Snapshot Test Results</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Raw Snapshot Data</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {JSON.stringify(rawSnapshot, null, 2)}
        </pre>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Converted KPIs ({kpis.length})</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {kpis.map((kpi) => (
            <div key={kpi.id} className="bg-white border rounded-lg p-4 shadow">
              <h3 className="font-semibold">{kpi.title}</h3>
              <p className="text-2xl font-bold text-blue-600">{kpi.currentValue}</p>
              <p className="text-sm text-gray-600">{kpi.description}</p>
              <p className="text-xs text-gray-500">
                Priority: {kpi.isPriority ? 'Yes' : 'No'} | Format: {kpi.format}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestSnapshot;
