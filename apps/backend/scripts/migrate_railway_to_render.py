#!/usr/bin/env python3
"""
Script para migrar dados do PostgreSQL do Railway para o Render.

Este script conecta aos bancos de origem (Railway) e destino (Render)
e migra os dados do banco learning.
"""
import os
import sys
import subprocess
import tempfile
from pathlib import Path

def run_command(cmd, description):
    """Execute a command and return success status."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=True)
        print(f"✅ {description} - Success")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e.stderr}")
        return False, e.stderr

def migrate_learning_database():
    """Migrate learning database from Railway to Render."""
    
    # Railway database URLs (from environment or manual input)
    railway_learning_url = os.getenv('RAILWAY_DATABASE_URL_LEARNING')
    if not railway_learning_url:
        print("❌ RAILWAY_DATABASE_URL_LEARNING not found in environment")
        railway_learning_url = input("Enter Railway learning database URL: ").strip()
    
    # Render database URLs (from environment or manual input)
    render_learning_url = os.getenv('DATABASE_URL_LEARNING')
    if not render_learning_url:
        print("❌ DATABASE_URL_LEARNING not found in environment")
        render_learning_url = input("Enter Render learning database URL: ").strip()
    
    if not railway_learning_url or not render_learning_url:
        print("❌ Missing database URLs. Aborting migration.")
        return False
    
    print("🚀 Starting PostgreSQL learning database migration...")
    print(f"📤 Source (Railway): ...{railway_learning_url[-30:]}")
    print(f"📥 Target (Render): ...{render_learning_url[-30:]}")
    
    # Create temporary file for backup
    with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as temp_file:
        backup_file = temp_file.name
    
    try:
        # Step 1: Create backup from Railway
        dump_cmd = f'pg_dump "{railway_learning_url}" > {backup_file}'
        success, output = run_command(dump_cmd, "Creating backup from Railway database")
        
        if not success:
            return False
        
        # Check if backup file has content
        backup_path = Path(backup_file)
        if not backup_path.exists() or backup_path.stat().st_size == 0:
            print("❌ Backup file is empty or doesn't exist")
            return False
        
        print(f"📊 Backup size: {backup_path.stat().st_size / 1024:.1f} KB")
        
        # Step 2: Restore to Render database
        restore_cmd = f'psql "{render_learning_url}" < {backup_file}'
        success, output = run_command(restore_cmd, "Restoring backup to Render database")
        
        if not success:
            return False
        
        # Step 3: Verify migration
        verify_cmd = f'psql "{render_learning_url}" -c "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = \'public\';"'
        success, output = run_command(verify_cmd, "Verifying migration")
        
        if success:
            print("✅ Migration completed successfully!")
            print(f"Database verification: {output.strip()}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ Migration failed with exception: {e}")
        return False
    
    finally:
        # Clean up temporary file
        try:
            os.unlink(backup_file)
            print(f"🧹 Cleaned up temporary backup file")
        except Exception as e:
            print(f"⚠️ Failed to clean up temporary file: {e}")

def check_postgresql_tools():
    """Check if PostgreSQL tools are available."""
    tools = ['pg_dump', 'psql']
    missing_tools = []
    
    for tool in tools:
        cmd = f"which {tool}"
        success, _ = run_command(cmd, f"Checking {tool}")
        if not success:
            missing_tools.append(tool)
    
    if missing_tools:
        print(f"❌ Missing PostgreSQL tools: {missing_tools}")
        print("Install with: brew install postgresql")
        return False
    
    print("✅ All required PostgreSQL tools are available")
    return True

def main():
    """Main migration function."""
    print("🗄️ DataHero4 Database Migration: Railway → Render")
    print("=" * 50)
    
    # Check prerequisites
    if not check_postgresql_tools():
        sys.exit(1)
    
    # Perform migration
    if migrate_learning_database():
        print("\n🎉 Migration completed successfully!")
        print("Next steps:")
        print("1. Update your application's DATABASE_URL_LEARNING")
        print("2. Test the application with the new database")
        print("3. Monitor for any issues")
    else:
        print("\n💥 Migration failed!")
        print("Please check the errors above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()