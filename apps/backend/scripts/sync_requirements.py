#!/usr/bin/env python3
"""
Script to sync requirements.txt from pyproject.toml using Poetry.
Useful for deployment platforms that prefer requirements.txt over Poetry.
"""
import subprocess
import sys
from pathlib import Path

def sync_requirements():
    """Generate requirements.txt from Poetry dependencies."""
    try:
        # Get the backend directory path
        backend_dir = Path(__file__).parent.parent
        
        print("🔄 Generating requirements.txt from Poetry dependencies...")
        
        # Get poetry show output
        result = subprocess.run(
            ["poetry", "show", "--no-dev"],
            cwd=backend_dir,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            # Try without --no-dev flag (older poetry versions)
            result = subprocess.run(
                ["poetry", "show"],
                cwd=backend_dir,
                capture_output=True,
                text=True,
                check=True
            )
        
        # Parse the output and generate requirements.txt
        requirements = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = line.split()
                if len(parts) >= 2:
                    package_name = parts[0]
                    package_version = parts[1]
                    requirements.append(f"{package_name}=={package_version}")
        
        # Write requirements.txt
        req_file = backend_dir / "requirements.txt"
        header = "# Generated from pyproject.toml by Poetry\n# Do not edit manually - run scripts/sync_requirements.py instead\n\n"
        
        with open(req_file, 'w') as f:
            f.write(header)
            for req in sorted(requirements):
                f.write(f"{req}\n")
        
        print(f"✅ Successfully generated requirements.txt with {len(requirements)} packages")
        print(f"📝 Updated {req_file}")
        return True
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Poetry show failed: {e}")
        print(f"stderr: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ Poetry not found. Make sure Poetry is installed and in PATH.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    if sync_requirements():
        print("🎉 Requirements sync completed successfully!")
        sys.exit(0)
    else:
        print("💥 Requirements sync failed!")
        sys.exit(1)