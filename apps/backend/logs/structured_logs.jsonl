{"timestamp": "2025-07-06T16:01:25.021364", "event": "snapshot_generation_start", "message": "Starting snapshot generation for client L2M", "system": "snapshot_system", "client_id": "L2M"}
{"timestamp": "2025-07-06T16:01:30.596868", "event": "kpi_calculation_success", "message": "KPI total_volume calculated successfully: 26757442623.04", "system": "snapshot_system", "kpi_id": "total_volume", "success": true, "duration_seconds": 2.7714641094207764, "value": 26757442623.04}
{"timestamp": "2025-07-06T16:01:33.350757", "event": "kpi_calculation_success", "message": "KPI average_ticket calculated successfully: 1130150.4740260178", "system": "snapshot_system", "kpi_id": "average_ticket", "success": true, "duration_seconds": 2.7537169456481934, "value": 1130150.4740260178}
{"timestamp": "2025-07-06T16:01:36.055651", "event": "kpi_calculation_success", "message": "KPI operations_per_analyst calculated successfully: 23684.0", "system": "snapshot_system", "kpi_id": "operations_per_analyst", "success": true, "duration_seconds": 2.704559087753296, "value": 23684.0}
{"timestamp": "2025-07-06T16:01:38.846151", "event": "kpi_calculation_success", "message": "KPI average_spread calculated successfully: 459.1302064873724", "system": "snapshot_system", "kpi_id": "average_spread", "success": true, "duration_seconds": 2.789777994155884, "value": 459.1302064873724}
{"timestamp": "2025-07-06T16:01:41.481309", "event": "kpi_calculation_success", "message": "KPI conversion_rate calculated successfully: 0.25", "system": "snapshot_system", "kpi_id": "conversion_rate", "success": true, "duration_seconds": 2.634786605834961, "value": 0.25}
{"timestamp": "2025-07-06T16:01:44.213510", "event": "kpi_calculation_success", "message": "KPI retention_rate calculated successfully: 25.33", "system": "snapshot_system", "kpi_id": "retention_rate", "success": true, "duration_seconds": 2.7318079471588135, "value": 25.33}
{"timestamp": "2025-07-06T16:01:44.215770", "event": "snapshot_generation_success", "message": "Snapshot generated successfully for L2M", "system": "snapshot_system", "client_id": "L2M", "kpi_count": 6, "success_rate": 100.0, "duration_seconds": 19.194379091262817, "performance_status": "good"}
{"timestamp": "2025-07-06T16:01:53.397991", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 130.80120086669922, "status_code": 200, "performance_status": "slow"}
{"timestamp": "2025-07-06T16:24:02.905513", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 7.951021194458008, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-06T16:24:02.909654", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.2579689025878906, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:14:55.293752", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 130.23710250854492, "status_code": 200, "performance_status": "slow"}
{"timestamp": "2025-07-08T09:16:39.074483", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.7410049438476562, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:16:39.077008", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.4260540008544922, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:16:39.079049", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.5002021789550781, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:16:39.082814", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.26798248291015625, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:16:39.084146", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.4210472106933594, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:16:39.085496", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.41294097900390625, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:19:56.250726", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.3859996795654297, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:20:03.306420", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.5130767822265625, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:20:11.414554", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.1537799835205078, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:21:19.987054", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.5249977111816406, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:23:12.235990", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.6041526794433594, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:23:12.238107", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.13399124145507812, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:23:22.361950", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.15616416931152344, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:26:24.885885", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.514984130859375, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T09:26:24.913413", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 1.1138916015625, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T10:09:26.987643", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 1.4917850494384766, "status_code": 200, "performance_status": "good"}
{"timestamp": "2025-07-08T10:09:27.060766", "event": "api_request", "message": "API request to /api/dashboard/snapshot", "system": "snapshot_system", "endpoint": "/api/dashboard/snapshot", "client_id": "L2M", "response_time_ms": 0.3619194030761719, "status_code": 200, "performance_status": "good"}
