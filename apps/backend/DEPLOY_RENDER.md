# 🚀 Deploy DataHero4 Backend no Render

Este guia explica como fazer deploy do backend DataHero4 no Render.com usando as melhores práticas.

## 📋 Pré-requisitos

- Conta no [Render.com](https://render.com)
- Repositório GitHub com o código do DataHero4
- Variáveis de ambiente configuradas

## 🔧 Configuração Automática via render.yaml

O backend já está configurado com um arquivo `render.yaml` para deploy automático:

```yaml
services:
  - type: web
    name: datahero4-backend
    runtime: docker
    region: oregon # ou frankfurt para LATAM
    plan: free # free tier para testes
    dockerfilePath: ./Dockerfile
    healthCheckPath: /health
    autoDeploy: true
```

## 🌟 Deploy Automático (Recomendado)

### 1. **Deploy via GitHub**

1. Acesse [Render Dashboard](https://dashboard.render.com)
2. Clique em **"New +"** → **"Web Service"**
3. Conecte seu repositório GitHub
4. Selecione o branch `main`
5. Configure:
   - **Name**: `datahero4-backend`
   - **Region**: `Oregon` (ou `Frankfurt` para LATAM)
   - **Plan**: `Free` (para testes)
   - **Root Directory**: `apps/backend`
   - **Runtime**: Será detectado automaticamente via Dockerfile

### 2. **Configurar Variáveis de Ambiente**

No painel do Render, adicione estas variáveis em **Environment**:

```bash
# Core settings
ENVIRONMENT=production
LOG_LEVEL=INFO

# Database
DATABASE_URL=<sua_connection_string_postgres>

# LLM APIs
TOGETHER_API_KEY=<sua_together_api_key>
ANTHROPIC_API_KEY=<sua_anthropic_api_key>
GOOGLE_API_KEY=<sua_google_api_key>
OPENAI_API_KEY=<sua_openai_api_key>

# Optional
REDIS_URL=<sua_redis_url>
DEFAULT_CLIENT_ID=L2M
DEFAULT_SECTOR=cambio
```

### 3. **Deploy**

- O deploy acontecerá automaticamente após conectar o GitHub
- Acompanhe os logs em **"Logs"** no dashboard
- A aplicação estará disponível na URL fornecida pelo Render

## 🐳 Deploy Manual via Docker

Se preferir deploy manual:

```bash
# Build local
docker build -t datahero4-backend .

# Test local
docker run -p 8000:8000 \
  -e DATABASE_URL="your_db_url" \
  -e TOGETHER_API_KEY="your_key" \
  datahero4-backend
```

## 🔍 Verificação do Deploy

### Health Check
```bash
curl https://sua-app.onrender.com/health
```

Resposta esperada:
```json
{
  "status": "healthy",
  "service": "datahero4-backend",
  "version": "4.0.0",
  "environment": "production",
  "port": "10000"
}
```

### Endpoints principais
- **API Docs**: `https://sua-app.onrender.com/docs` (só em desenvolvimento)
- **Health**: `https://sua-app.onrender.com/health`
- **Ready**: `https://sua-app.onrender.com/ready`
- **Chat API**: `https://sua-app.onrender.com/api/chat/`
- **Dashboard API**: `https://sua-app.onrender.com/api/dashboard/`

## ⚡ Otimizações

### 1. **Build Performance**
- Dockerfile multi-stage reduz tamanho da imagem
- `.dockerignore` otimizado acelera builds
- Cache de dependências Poetry

### 2. **Runtime Performance**
- Non-root user para segurança
- Health checks integrados
- Graceful shutdown

### 3. **Monitoring**
- Logs estruturados
- Endpoints de health/readiness
- Error tracking

## 🔄 Atualização de Dependências

Para atualizar o `requirements.txt` a partir do Poetry:

```bash
cd apps/backend
python3 scripts/sync_requirements.py
```

## 🆘 Troubleshooting

### Build falha
1. Verifique se todas as dependências estão no `pyproject.toml`
2. Execute `python3 scripts/sync_requirements.py` se usar requirements.txt
3. Teste build local: `docker build -t test .`

### Health check falha
1. Verifique se a aplicação está escutando na porta `$PORT`
2. Confirme que `/health` endpoint está disponível
3. Verifique logs no dashboard do Render

### Variáveis de ambiente
1. Confirme que todas as variáveis necessárias estão definidas
2. Use logs para debug: verifique se as variáveis estão sendo carregadas
3. Ambiente de desenvolvimento vs produção

## 🎯 Próximos Passos

1. **Database**: Adicionar PostgreSQL managed pelo Render
2. **Cache**: Configurar Redis para cache
3. **Monitoring**: Configurar alertas e métricas
4. **SSL**: Configurar domínio customizado
5. **CI/CD**: Automatizar testes antes do deploy

## 🔗 Links Úteis

- [Render Docs](https://render.com/docs)
- [FastAPI Deployment Guide](https://render.com/docs/deploy-fastapi)
- [Docker Best Practices](https://render.com/docs/docker)
- [Environment Variables](https://render.com/docs/configure-environment-variables)

---

✅ **Deploy configurado com sucesso!** 

Sua aplicação DataHero4 está agora rodando no Render com:
- ✅ Multi-stage Docker build otimizado
- ✅ Health checks configurados
- ✅ Auto-deploy do GitHub
- ✅ Free tier disponível
- ✅ Logs e monitoring integrados