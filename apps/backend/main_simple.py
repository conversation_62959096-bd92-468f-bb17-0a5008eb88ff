"""
Simplified FastAPI app for Railway deployment testing.
"""
import os
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Configure logging
logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI app instance
app = FastAPI(
    title="DataHero4 Backend - Simple",
    description="Simplified DataHero4 backend for Railway deployment",
    version="4.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check() -> JSONResponse:
    """Health check endpoint for Railway load balancer."""
    try:
        health_data = {
            "status": "healthy",
            "service": "datahero4-backend-simple",
            "version": "4.0.0",
            "environment": os.getenv("ENVIRONMENT", "development"),
            "port": os.getenv("PORT", "8000"),
        }
        
        return JSONResponse(
            status_code=200,
            content=health_data
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "datahero4-backend-simple",
                "error": str(e)
            }
        )

# Simple test endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "DataHero4 Backend is running!",
        "status": "ok",
        "version": "4.0.0"
    }

# Test endpoint
@app.get("/test")
async def test():
    """Test endpoint."""
    return {
        "message": "Test endpoint working!",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "port": os.getenv("PORT", "8000")
    }

# Development server runner
if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")
    
    logger.info(f"Starting simple server on {host}:{port}")
    
    uvicorn.run(
        app, 
        host=host, 
        port=port,
        log_level=os.getenv("LOG_LEVEL", "info").lower(),
        access_log=True
    )
