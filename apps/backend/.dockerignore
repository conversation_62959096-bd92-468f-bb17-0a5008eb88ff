# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/

# VSCode
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Environment files
.env.local
.env.development
.env.test
.env.production

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Testing
coverage.xml
*.cover
.coverage
.cache
nosetests.xml

# Documentation
docs/
*.md
README.md

# Git
.git/
.gitignore

# Railway/Render specific (keep these out of Docker context)
railway.json
nixpacks.toml
render.yaml
.railway.json

# Development tools
.pre-commit-config.yaml
pyproject.toml.lock

# Database files (if any local ones)
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.tmp/