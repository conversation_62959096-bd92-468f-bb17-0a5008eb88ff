services:
  - type: web
    name: datahero4-backend
    runtime: docker
    region: oregon # ou frankfurt para LATAM
    plan: free # free tier para testes
    dockerfilePath: ./Dockerfile
    healthCheckPath: /health
    envVars:
      # Core application settings
      - key: ENVIRONMENT
        value: production
      - key: LOG_LEVEL
        value: INFO
      
      # Database configuration
      - key: DATABASE_URL
        sync: false # Set manually in Render dashboard (AWS RDS)
      - key: DATABASE_URL_LEARNING
        fromDatabase:
          name: datahero4-learning-db
          property: connectionString
      
      # LLM Provider API Keys
      - key: TOGETHER_API_KEY
        sync: false # Set manually in Render dashboard
      - key: ANTHROPIC_API_KEY
        sync: false # Set manually in Render dashboard
      - key: GOOGLE_API_KEY
        sync: false # Set manually in Render dashboard
      - key: OPENAI_API_KEY
        sync: false # Set manually in Render dashboard
      
      # Optional services
      - key: REDIS_URL
        sync: false # Set manually in Render dashboard if using Redis
      
      # Application specific
      - key: DEFAULT_CLIENT_ID
        value: L2M
      - key: DEFAULT_SECTOR
        value: cambio
    
    # Auto-deploy on push to main branch
    autoDeploy: true
    
    # Build settings
    dockerCommand: uvicorn main:app --host 0.0.0.0 --port $PORT

# PostgreSQL databases
databases:
  - name: datahero4-learning-db
    databaseName: datahero4_learning
    user: datahero4_user
    plan: free # free tier para testes
    region: oregon