# 🚛 Guia de Migração: Railway → Render

Este guia detalha como migrar completamente o DataHero4 do Railway para o Render, incluindo bancos de dados e aplicação.

## 📋 Visão Geral da Migração

### Bancos de Dados Identificados:
1. **DATABASE_URL** (Principal): AWS RDS externo - `l2m-homol.c50woq6q6gnz.us-east-1.rds.amazonaws.com`
2. **DATABASE_URL_LEARNING** (Learning): Railway PostgreSQL interno - `postgres.railway.internal`

### Estratégia de Migração:
- ✅ **Banco Principal**: Manter AWS RDS (apenas atualizar connection string)
- 🔄 **Banco Learning**: Migrar do Railway para Render PostgreSQL

## 🎯 Passos da Migração

### 1. **Preparação no Render**

#### 1.1. Deploy via GitHub (Automático)
```bash
# O render.yaml já está configurado para:
# - Web service (datahero4-backend)
# - PostgreSQL database (datahero4-learning-db)
```

1. Acesse [Render Dashboard](https://dashboard.render.com)
2. Click **"New +"** → **"Blueprint"**
3. Conecte o repositório GitHub: `datahero4`
4. Selecione branch `main`
5. Configure root directory: `apps/backend`
6. O Render detectará automaticamente o `render.yaml`

#### 1.2. Verificar Recursos Criados
Após o deploy do Blueprint, você terá:
- ✅ **Web Service**: `datahero4-backend`
- ✅ **PostgreSQL**: `datahero4-learning-db` (novo, vazio)

### 2. **Configurar Variáveis de Ambiente**

#### 2.1. Variáveis Manuais (no Dashboard)
Configure estas no painel do Render:

```bash
# Core settings
ENVIRONMENT=production
LOG_LEVEL=INFO

# Banco principal (AWS RDS - mesmo do Railway)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/l2m_prod

# LLM APIs (mesmas do Railway)
TOGETHER_API_KEY=fw_3Ze846V6V6iZQuFrQq2Jkk3C
ANTHROPIC_API_KEY=************************************************************************************************************
GROQ_API_KEY=********************************************************
FIREWORKS_API_KEY=fw_3Ze846V6V6iZQuFrQq2Jkk3C

# Optional
DEFAULT_CLIENT_ID=L2M
DEFAULT_SECTOR=cambio
CORS_ALLOW_ORIGINS=https://your-frontend.onrender.com,http://localhost:3000
```

#### 2.2. Variáveis Automáticas
O render.yaml já configura automaticamente:
- `DATABASE_URL_LEARNING` → Connection string do PostgreSQL do Render

### 3. **Migração do Banco Learning**

#### 3.1. Backup do Railway (Manual)
Como o `postgres.railway.internal` não é acessível externamente:

1. **Via Railway Dashboard**:
   - Acesse Railway Dashboard → PostgreSQL service
   - Connect → Download backup
   - Ou use pgAdmin/DBeaver conectando via proxy

2. **Via Railway CLI** (se funcionar):
   ```bash
   railway connect postgres
   # Em outra aba:
   pg_dump postgresql://localhost:5432/railway > railway_learning_backup.sql
   ```

#### 3.2. Restore no Render
```bash
# Obter connection string do Render (Dashboard → Database → Connection String)
export RENDER_LEARNING_URL="postgresql://user:pass@host:port/db"

# Restore backup
psql "$RENDER_LEARNING_URL" < railway_learning_backup.sql

# Verificar migração
psql "$RENDER_LEARNING_URL" -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"
```

#### 3.3. Script Automatizado
```bash
cd apps/backend
python3 scripts/migrate_railway_to_render.py
```

### 4. **Teste e Verificação**

#### 4.1. Health Check
```bash
curl https://your-app.onrender.com/health
```

Resposta esperada:
```json
{
  "status": "healthy",
  "service": "datahero4-backend",
  "version": "4.0.0",
  "environment": "production"
}
```

#### 4.2. Test Database Connections
```bash
# Test learning database
curl https://your-app.onrender.com/api/health/database

# Test main application
curl https://your-app.onrender.com/api/chat/health
```

### 5. **Configuração do Frontend**

Atualize as URLs do frontend para apontar para o Render:
```bash
# Frontend environment variables
VITE_API_URL=https://datahero4-backend.onrender.com
```

### 6. **DNS e Domínio (Opcional)**

Se quiser usar domínio customizado:
```bash
# No Render Dashboard → Settings → Custom Domains
# Adicionar: api.datahero4.com → datahero4-backend.onrender.com
```

## 🔧 Comandos Úteis

### Render CLI
```bash
# Login
render login

# List services
render services -o json

# View logs
render logs -o text

# Open database CLI
render psql
```

### Database Management
```bash
# Backup Render database
pg_dump "$DATABASE_URL_LEARNING" > render_backup.sql

# Monitor database
psql "$DATABASE_URL_LEARNING" -c "SELECT version();"
```

## ⚠️ Troubleshooting

### Build Fails
1. Check logs: Render Dashboard → Logs
2. Verify Dockerfile syntax
3. Test local build: `docker build -t test .`

### Database Connection Issues
1. Verify connection strings in environment variables
2. Check database status in Render Dashboard
3. Test connections manually with psql

### Environment Variables
1. Ensure all required variables are set
2. Check that DATABASE_URL_LEARNING is automatically populated
3. Verify sensitive data is marked as "sync: false"

## 📊 Comparação Custos

| Recurso | Railway | Render |
|---------|---------|---------|
| Web Service | $30/mês | Free tier |
| PostgreSQL | $32/mês | Free tier |
| **Total** | **$62/mês** | **$0/mês** (initially) |

## ✅ Checklist de Migração

- [ ] Deploy Blueprint no Render
- [ ] Configurar variáveis de ambiente
- [ ] Backup banco learning do Railway
- [ ] Criar banco learning no Render
- [ ] Migrar dados learning
- [ ] Testar health checks
- [ ] Testar funcionalidade da aplicação
- [ ] Atualizar frontend URLs
- [ ] Configurar domínio (opcional)
- [ ] Monitorar por 24h
- [ ] Desligar serviços Railway

## 🎯 Pós-Migração

1. **Monitoramento**: Configurar alertas no Render
2. **Backup**: Agendar backups regulares
3. **Performance**: Monitorar metrics e logs
4. **Scale**: Avaliar se free tier é suficiente

---

**Tempo estimado**: 2-3 horas  
**Downtime**: ~15-30 minutos durante a troca de DNS