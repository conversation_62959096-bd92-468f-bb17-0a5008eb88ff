"""
Unified Validation Node - Consolidação Inteligente de Validação
==============================================================

Este node unifica todas as funcionalidades de validação do DataHero4:
1. Validação SQL básica (syntax, semantics) - do validation_node.py
2. Validação temporal híbrida - do temporal_validation_node.py  
3. Validação avançada de schema - do comprehensive_validation_node.py
4. Business rules engine - do comprehensive_validation_node.py
5. Error handling gracioso - do comprehensive_validation_node.py

Benefícios da Unificação:
- Performance: 1 chamada LLM em vez de 3
- Latência: ~300ms em vez de ~900ms
- Manutenção: 1 arquivo em vez de 3
- Funcionalidade: Todas as validações preservadas
- Inteligência: Detecção automática do tipo de validação necessária
"""

import logging
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass

from ..graphs.state import DataHeroState
from ..agents.query_validator import query_validator_agent
from ..validation.temporal_validator import TemporalValidator, ValidationResult, ErrorType
from ..validation.hybrid_temporal_validator import HybridTemporalValidator, ValidationPath
from ..validation.enhanced_schema_validator import create_enhanced_schema_validator, EnhancedValidationResult
from ..validation.business_rules_engine import create_business_rules_engine, create_rule_context, RuleViolation
from ..validation.graceful_error_handler import create_graceful_error_handler, MessageChannel
from ..utils.metrics import record_validation_metric

logger = logging.getLogger(__name__)


@dataclass
class ValidationStrategy:
    """Estratégia de validação baseada no contexto da query."""
    use_basic_validation: bool = True
    use_temporal_validation: bool = True
    use_schema_validation: bool = True
    use_business_rules: bool = True
    enable_auto_correction: bool = True
    performance_mode: str = "balanced"  # "fast", "balanced", "comprehensive"


@dataclass
class UnifiedValidationResult:
    """Resultado consolidado de todas as validações."""
    is_valid: bool
    validation_errors: List[Dict[str, Any]]
    validation_warnings: List[Dict[str, Any]]
    corrected_sql: Optional[str] = None
    corrections_applied: List[str] = None
    temporal_result: Optional[ValidationResult] = None
    schema_issues: List[Any] = None
    business_violations: List[RuleViolation] = None
    user_friendly_messages: List[str] = None
    performance_metrics: Dict[str, float] = None
    next_action: str = "proceed"  # "proceed", "proceed_with_warnings", "block_execution"


class UnifiedValidationNode:
    """
    Node de validação unificado que integra todas as validações do DataHero4.
    
    Executa validação em pipeline otimizado:
    1. Detecção automática da estratégia de validação
    2. Validação SQL básica (sempre)
    3. Validação temporal (se necessário)
    4. Validação de schema avançada (se habilitada)
    5. Validação de regras de negócio (se aplicável)
    6. Processamento de erros gracioso
    """
    
    def __init__(self,
                 domain: str = "cambio",
                 enable_auto_correction: bool = True,
                 message_channel: MessageChannel = MessageChannel.API,
                 rules_directory: Optional[str] = None,
                 performance_mode: str = "balanced"):
        """
        Inicializa o node de validação unificado.

        Args:
            domain: Domínio específico (cambio, geral)
            enable_auto_correction: Se deve aplicar correções automáticas
            message_channel: Canal para formatação de mensagens
            rules_directory: Diretório com regras de negócio
            performance_mode: Modo de performance ("fast", "balanced", "comprehensive")
        """
        self.domain = domain
        self.enable_auto_correction = enable_auto_correction
        self.message_channel = message_channel
        self.performance_mode = performance_mode

        # Inicializa componentes de validação
        self._initialize_validators(rules_directory)
        # Métricas de performance
        self.metrics = {
            "total_validations": 0,
            "validation_time_ms": [],
            "auto_corrections_applied": 0,
            "temporal_validations": 0,
            "schema_validations": 0,
            "business_rule_validations": 0,
            "fallback_activations": 0
        }

        logger.info(f"🔧 UnifiedValidationNode initialized for domain '{domain}' in '{performance_mode}' mode")

    def _initialize_validators(self, rules_directory: Optional[str]):
        """Inicializa todos os validadores de forma lazy."""
        try:
            # 1. Temporal Validator (híbrido) - sempre inicializado
            self.temporal_validator = HybridTemporalValidator()

            # 2. Enhanced Schema Validator - inicializado se necessário
            self.schema_validator = None
            if self.performance_mode in ["balanced", "comprehensive"]:
                self.schema_validator = create_enhanced_schema_validator(
                    domain=self.domain,
                    enable_auto_correction=self.enable_auto_correction
                )

            # 3. Business Rules Engine - inicializado se necessário
            self.rules_engine = None
            if self.performance_mode == "comprehensive":
                rules_dir = rules_directory or "src/config/business_rules"
                self.rules_engine = create_business_rules_engine(
                    domain=self.domain,
                    rules_directory=rules_dir
                )
                self.rules_engine.load_rules()

            # 4. Error Handler - sempre inicializado
            self.error_handler = create_graceful_error_handler(
                channel=self.message_channel
            )

            logger.info(f"✅ Validation components initialized for {self.performance_mode} mode")

        except Exception as e:
            logger.error(f"❌ Failed to initialize validators: {e}")
            # Fallback para modo básico
            self.temporal_validator = TemporalValidator()
            self.schema_validator = None
            self.rules_engine = None
            self.error_handler = create_graceful_error_handler(channel=self.message_channel)
    
    def __call__(self, state: DataHeroState) -> DataHeroState:
        """
        Executa validação unificada no estado do LangGraph.
        
        Args:
            state: Estado atual do workflow
            
        Returns:
            Estado atualizado com resultados de validação
        """
        start_time = time.perf_counter()
        self.metrics["total_validations"] += 1
        
        try:
            # Extrai dados necessários do estado
            sql_query = state.get("sql_query", "")
            question = state.get("question", "")
            client_id = state.get("client_id", "")
            sector = state.get("sector", "")
            
            if not sql_query:
                return self._handle_no_sql_query(state)
            
            logger.info(f"🔍 Starting unified validation for query: {sql_query[:100]}...")
            
            # Determina estratégia de validação
            strategy = self._determine_validation_strategy(state, question, sql_query)
            
            # Executa pipeline de validação
            validation_result = self._execute_unified_validation_pipeline(
                question, sql_query, state, strategy
            )
            
            # Atualiza estado com resultados
            updated_state = self._update_state_with_unified_results(state, validation_result)
            
            # Log métricas
            duration_ms = (time.perf_counter() - start_time) * 1000
            self.metrics["validation_time_ms"].append(duration_ms)
            
            logger.info(f"✅ Unified validation completed in {duration_ms:.2f}ms - Valid: {validation_result.is_valid}")
            
            return updated_state
            
        except Exception as e:
            logger.error(f"❌ Critical error in unified validation: {e}")
            self.metrics["fallback_activations"] += 1
            return self._handle_validation_error(state, str(e))
    
    def _determine_validation_strategy(self, state: DataHeroState, question: str, sql_query: str) -> ValidationStrategy:
        """
        Determina automaticamente a estratégia de validação baseada no contexto.
        
        Args:
            state: Estado atual
            question: Pergunta do usuário
            sql_query: Query SQL gerada
            
        Returns:
            Estratégia de validação otimizada
        """
        strategy = ValidationStrategy()
        
        # Detecção inteligente de necessidade de validação temporal
        temporal_keywords = ["futuro", "próximo", "2025", "2026", "amanhã", "semana que vem"]
        strategy.use_temporal_validation = any(keyword in question.lower() for keyword in temporal_keywords)
        
        # Detecção de necessidade de validação de schema avançada
        complex_sql_patterns = ["JOIN", "SUBQUERY", "UNION", "HAVING", "WINDOW"]
        strategy.use_schema_validation = any(pattern in sql_query.upper() for pattern in complex_sql_patterns)
        
        # Detecção de necessidade de business rules
        business_keywords = ["compliance", "regulamentação", "limite", "política"]
        strategy.use_business_rules = any(keyword in question.lower() for keyword in business_keywords)
        
        # Ajusta performance mode baseado na complexidade
        if strategy.use_temporal_validation and strategy.use_schema_validation and strategy.use_business_rules:
            strategy.performance_mode = "comprehensive"
        elif strategy.use_temporal_validation or strategy.use_schema_validation:
            strategy.performance_mode = "balanced"
        else:
            strategy.performance_mode = "fast"
        
        logger.debug(f"🎯 Validation strategy: {strategy.performance_mode} mode")
        return strategy

    def _execute_unified_validation_pipeline(self,
                                           question: str,
                                           sql_query: str,
                                           state: DataHeroState,
                                           strategy: ValidationStrategy) -> UnifiedValidationResult:
        """
        Executa pipeline de validação unificado de forma otimizada.

        Args:
            question: Pergunta do usuário
            sql_query: Query SQL para validar
            state: Estado atual
            strategy: Estratégia de validação

        Returns:
            Resultado consolidado de todas as validações
        """
        result = UnifiedValidationResult(
            is_valid=True,
            validation_errors=[],
            validation_warnings=[],
            corrections_applied=[],
            performance_metrics={}
        )

        # ETAPA 1: Validação SQL Básica (sempre executada)
        basic_start = time.perf_counter()
        basic_result = self._execute_basic_validation(state)
        result.performance_metrics["basic_validation_ms"] = (time.perf_counter() - basic_start) * 1000

        # Aplica resultados da validação básica
        if not basic_result.get("sql_valid", False):
            result.is_valid = False
            result.validation_errors.extend([
                {"type": "basic_sql", "message": error, "severity": "critical"}
                for error in basic_result.get("validation_errors", [])
            ])

        # Se validação básica falhou criticamente, para aqui
        if not result.is_valid and any(e.get("severity") == "critical" for e in result.validation_errors):
            result.next_action = "block_execution"
            return result

        # ETAPA 2: Validação Temporal (se necessária)
        if strategy.use_temporal_validation:
            temporal_start = time.perf_counter()
            temporal_result = self._execute_temporal_validation(question, sql_query)
            result.performance_metrics["temporal_validation_ms"] = (time.perf_counter() - temporal_start) * 1000
            result.temporal_result = temporal_result
            self.metrics["temporal_validations"] += 1

            if not temporal_result.valid:
                result.is_valid = False
                result.validation_errors.append({
                    "type": "temporal",
                    "message": temporal_result.message,
                    "severity": "blocking",
                    "confidence": temporal_result.confidence_score
                })
                result.next_action = "block_execution"
                return result

        # ETAPA 3: Validação de Schema Avançada (se habilitada)
        if strategy.use_schema_validation and self.schema_validator:
            schema_start = time.perf_counter()
            schema_result = self._execute_schema_validation(sql_query, state)
            result.performance_metrics["schema_validation_ms"] = (time.perf_counter() - schema_start) * 1000
            result.schema_issues = schema_result.issues_found if schema_result else []
            self.metrics["schema_validations"] += 1

            # Aplica correções automáticas se disponíveis
            if schema_result and schema_result.corrected_sql and schema_result.corrected_sql != sql_query:
                result.corrected_sql = schema_result.corrected_sql
                result.corrections_applied.extend(schema_result.corrections_applied)
                self.metrics["auto_corrections_applied"] += len(schema_result.corrections_applied)
                logger.info(f"🔧 Applied {len(schema_result.corrections_applied)} auto-corrections")

            # Adiciona issues críticos aos erros
            if schema_result:
                critical_issues = [issue for issue in schema_result.issues_found if issue.severity == "critical"]
                if critical_issues:
                    result.validation_errors.extend([{
                        "type": "schema_critical",
                        "message": issue.description,
                        "severity": issue.severity
                    } for issue in critical_issues])

        # ETAPA 4: Validação de Regras de Negócio (se aplicável)
        if strategy.use_business_rules and self.rules_engine:
            business_start = time.perf_counter()
            business_violations = self._execute_business_rules_validation(
                result.corrected_sql or sql_query, state
            )
            result.performance_metrics["business_rules_ms"] = (time.perf_counter() - business_start) * 1000
            result.business_violations = business_violations
            self.metrics["business_rule_validations"] += 1

            # Adiciona violações críticas aos erros
            critical_violations = [v for v in business_violations if v.severity == "critical"]
            if critical_violations:
                result.validation_errors.extend([{
                    "type": "business_rule",
                    "message": v.message,
                    "severity": v.severity
                } for v in critical_violations])

        # ETAPA 5: Processamento de Mensagens User-Friendly
        if result.validation_errors and self.error_handler:
            user_messages = self.error_handler.process_validation_errors(result.validation_errors)
            result.user_friendly_messages = user_messages

        # Determina ação final
        if result.validation_errors:
            critical_errors = [e for e in result.validation_errors if e.get("severity") in ["blocking", "critical"]]
            if critical_errors:
                result.next_action = "block_execution"
            else:
                result.next_action = "proceed_with_warnings"
        else:
            result.next_action = "proceed"

        return result

    def _execute_basic_validation(self, state: DataHeroState) -> Dict[str, Any]:
        """Executa validação SQL básica usando o query_validator_agent."""
        try:
            return query_validator_agent(state)
        except Exception as e:
            logger.error(f"❌ Basic validation failed: {e}")
            return {
                "sql_valid": False,
                "validation_errors": [f"Basic validation error: {str(e)}"],
                "semantic_validation_passed": False
            }

    def _execute_temporal_validation(self, question: str, sql_query: str) -> ValidationResult:
        """Executa validação temporal híbrida."""
        try:
            return self.temporal_validator.validate_query_temporality(question, sql_query)
        except Exception as e:
            logger.error(f"❌ Temporal validation failed: {e}")
            return ValidationResult(
                valid=False,
                error_type=ErrorType.VALIDATION_ERROR,
                message=f"Temporal validation error: {str(e)}",
                confidence_score=0.0
            )

    def _execute_schema_validation(self, sql_query: str, state: DataHeroState) -> Optional[EnhancedValidationResult]:
        """Executa validação de schema avançada."""
        try:
            context = {
                "domain": self.domain,
                "sector": state.get("sector"),
                "client_id": state.get("client_id"),
                "user_role": state.get("user_role")
            }
            return self.schema_validator.validate_business_logic(sql_query, context)
        except Exception as e:
            logger.error(f"❌ Schema validation failed: {e}")
            return None

    def _execute_business_rules_validation(self, sql_query: str, state: DataHeroState) -> List[RuleViolation]:
        """Executa validação de regras de negócio."""
        try:
            rule_context = create_rule_context(
                domain=self.domain,
                sector=state.get("sector"),
                client_id=state.get("client_id"),
                user_role=state.get("user_role")
            )
            return self.rules_engine.validate_business_rules(sql_query, rule_context)
        except Exception as e:
            logger.error(f"❌ Business rules validation failed: {e}")
            return []

    def _update_state_with_unified_results(self, state: DataHeroState, result: UnifiedValidationResult) -> DataHeroState:
        """
        Atualiza o estado do LangGraph com os resultados da validação unificada.

        Args:
            state: Estado original
            result: Resultado da validação unificada

        Returns:
            Estado atualizado
        """
        updated_state = {
            **state,
            # Campos principais de validação
            "query_valid": result.is_valid,
            "sql_valid": result.is_valid,
            "validation_errors": [e["message"] for e in result.validation_errors],
            "validation_warnings": [w["message"] for w in result.validation_warnings],
            "semantic_validation_passed": result.is_valid,

            # SQL corrigido se disponível
            "sql_query": result.corrected_sql or state.get("sql_query"),

            # Próxima ação
            "next_action": result.next_action,

            # Metadados detalhados para debugging
            "_debug_info": {
                **state.get("_debug_info", {}),
                "unified_validation": {
                    "valid": result.is_valid,
                    "error_count": len(result.validation_errors),
                    "warning_count": len(result.validation_warnings),
                    "corrections_applied": len(result.corrections_applied or []),
                    "next_action": result.next_action,
                    "performance_metrics": result.performance_metrics,
                    "timestamp": datetime.now().isoformat()
                }
            }
        }

        # Adiciona campos específicos se disponíveis
        if result.temporal_result:
            updated_state["temporal_validation_passed"] = result.temporal_result.valid
            if not result.temporal_result.valid:
                updated_state["temporal_validation_error"] = result.temporal_result.message
                updated_state["suggested_questions"] = result.temporal_result.suggested_alternatives

        if result.corrections_applied:
            updated_state["auto_corrections_applied"] = result.corrections_applied

        if result.user_friendly_messages:
            updated_state["user_messages"] = result.user_friendly_messages

        # Adiciona detalhes de validação para debugging
        updated_state["validation_details"] = {
            "temporal_passed": result.temporal_result.valid if result.temporal_result else True,
            "schema_issues_count": len(result.schema_issues or []),
            "business_violations_count": len(result.business_violations or []),
            "corrections_applied_count": len(result.corrections_applied or [])
        }

        return updated_state

    def _handle_no_sql_query(self, state: DataHeroState) -> DataHeroState:
        """Trata caso onde não há SQL query para validar."""
        return {
            **state,
            "query_valid": False,
            "sql_valid": False,
            "validation_errors": ["No SQL query to validate"],
            "semantic_validation_passed": False,
            "next_action": "block_execution",
            "_debug_info": {
                **state.get("_debug_info", {}),
                "unified_validation": {
                    "valid": False,
                    "error": "No SQL query provided",
                    "timestamp": datetime.now().isoformat()
                }
            }
        }

    def _handle_validation_error(self, state: DataHeroState, error_message: str) -> DataHeroState:
        """Trata erros críticos durante a validação."""
        return {
            **state,
            "query_valid": False,
            "sql_valid": False,
            "validation_errors": [f"Validation system error: {error_message}"],
            "semantic_validation_passed": False,
            "next_action": "block_execution",
            "_debug_info": {
                **state.get("_debug_info", {}),
                "unified_validation": {
                    "valid": False,
                    "critical_error": error_message,
                    "fallback_activated": True,
                    "timestamp": datetime.now().isoformat()
                }
            }
        }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Retorna métricas de performance do validador."""
        avg_time = sum(self.metrics["validation_time_ms"]) / len(self.metrics["validation_time_ms"]) if self.metrics["validation_time_ms"] else 0

        return {
            "total_validations": self.metrics["total_validations"],
            "average_validation_time_ms": avg_time,
            "auto_corrections_applied": self.metrics["auto_corrections_applied"],
            "temporal_validations": self.metrics["temporal_validations"],
            "schema_validations": self.metrics["schema_validations"],
            "business_rule_validations": self.metrics["business_rule_validations"],
            "fallback_activations": self.metrics["fallback_activations"],
            "performance_mode": self.performance_mode
        }


# Factory functions para fácil integração

def create_unified_validation_node(
    domain: str = "cambio",
    enable_auto_correction: bool = True,
    message_channel: MessageChannel = MessageChannel.API,
    performance_mode: str = "balanced"
) -> UnifiedValidationNode:
    """
    Cria node de validação unificado configurado.

    Args:
        domain: Domínio específico
        enable_auto_correction: Se deve aplicar correções automáticas
        message_channel: Canal para mensagens
        performance_mode: Modo de performance

    Returns:
        UnifiedValidationNode configurado
    """
    return UnifiedValidationNode(
        domain=domain,
        enable_auto_correction=enable_auto_correction,
        message_channel=message_channel,
        performance_mode=performance_mode
    )


def unified_validation_node(state: DataHeroState) -> DataHeroState:
    """
    Função wrapper para integração direta no LangGraph.

    Args:
        state: Estado do grafo

    Returns:
        Estado atualizado com validação unificada
    """
    # Extrai configurações do estado se disponíveis
    domain = state.get("domain", state.get("sector", "cambio"))
    channel = MessageChannel(state.get("channel", "api"))
    performance_mode = state.get("validation_performance_mode", "balanced")

    # Cria e executa node unificado
    validation_node = create_unified_validation_node(
        domain=domain,
        message_channel=channel,
        performance_mode=performance_mode
    )

    return validation_node(state)


# Função de conveniência para testes
def validate_unified_query(question: str, sql: str, domain: str = "cambio") -> Dict[str, Any]:
    """
    Testa validação unificada independentemente do LangGraph.

    Args:
        question: Pergunta do usuário
        sql: Query SQL
        domain: Domínio

    Returns:
        Resultados de validação para teste
    """
    test_state = {
        "question": question,
        "sql_query": sql,
        "domain": domain,
        "sector": domain,
        "client_id": "test",
        "channel": "api"
    }

    result_state = unified_validation_node(test_state)

    return {
        "valid": result_state.get("query_valid", False),
        "errors": result_state.get("validation_errors", []),
        "warnings": result_state.get("validation_warnings", []),
        "corrected_sql": result_state.get("sql_query") if result_state.get("auto_corrections_applied") else None,
        "next_action": result_state.get("next_action", "unknown"),
        "debug_info": result_state.get("_debug_info", {}).get("unified_validation", {})
    }
