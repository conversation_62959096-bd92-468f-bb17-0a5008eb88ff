# 📊 Plano de Implementação: Geração Automática de Queries KPI

## 🎯 **Desafio**

O sistema atual do DataHero4 tem queries KPI **hardcoded** no código Python, o que viola princípios fundamentais:

- ❌ **Manutenibilidade**: Mudanças requerem deploy
- ❌ **Flexibilidade**: Não permite configuração dinâmica  
- ❌ **Escalabilidade**: Cada novo KPI requer alteração de código
- ❌ **Separação de responsabilidades**: Lógica de negócio misturada com código

### **Estado Atual**
```python
# ❌ PROBLEMA: Queries hardcoded
def _calculate_real_kpi_value(self, kpi_id: str):
    if kpi_id == 'total_volume':
        result = conn.execute(text('''
            SELECT COALESCE(SUM(valor_me), 0) as volume_total
            FROM boleta WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
        '''))
```

### **Estado Desejado**
```json
{
  "id": "total_volume",
  "name": "Volume Total Negociado",
  "sql_query": "SELECT COALESCE(SUM(valor_me), 0) FROM boleta WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months' AND id_cliente = :client_id",
  "parameters": ["client_id"],
  "format_type": "currency"
}
```

---

## 🏗️ **Solução: Sistema de Geração Automática**

### **Abordagem Inspirada em MCP + LangGraph**

Baseado nas melhores práticas do **Model Context Protocol** e **LangGraph**, vamos implementar um sistema que:

1. **Emula MCP Postgres** - Usa o próprio DataHero para descobrir queries
2. **Aplica LangGraph Patterns** - Workflow estruturado com validação
3. **Gera Queries Dinamicamente** - Para os 6 KPIs críticos primeiro
4. **Valida e Testa** - Cada query antes de salvar no JSON

---

## 🔄 **Arquitetura da Solução**

### **1. LangGraph Workflow para Geração de KPIs**

```python
# Baseado em: /langchain-ai/langgraph - SQL Agent Tutorial
class KpiGenerationState(TypedDict):
    kpi_definition: Dict[str, Any]  # KPI metadata
    generated_query: str           # SQL query gerada
    validation_result: Dict        # Resultado da validação
    test_result: Dict             # Resultado do teste
    final_kpi: Dict               # KPI final validado
```

### **2. Nodes do Workflow**

#### **Node 1: Schema Discovery** 
```python
# Inspirado em: MCP SQLite Explorer
@node
def discover_schema(state: KpiGenerationState):
    """Descobre schema da base de dados como MCP Postgres"""
    schema_info = get_database_schema()
    return {"schema": schema_info}
```

#### **Node 2: Query Generation**
```python
# Baseado em: LangGraph SQL Query Generation
@node  
def generate_kpi_query(state: KpiGenerationState):
    """Gera query SQL usando LLM com contexto do schema"""
    system_prompt = f"""
    Você é um especialista em SQL para KPIs de câmbio.
    
    Schema disponível: {state['schema']}
    KPI: {state['kpi_definition']['name']}
    Descrição: {state['kpi_definition']['description']}
    
    Gere uma query SQL otimizada que:
    - Use apenas tabelas existentes
    - Seja performática (single query)
    - Retorne valor numérico
    - Use parâmetros para client_id
    """
    
    query = llm_with_tools.invoke([
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"Gere SQL para: {state['kpi_definition']}")
    ])
    
    return {"generated_query": query.content}
```

#### **Node 3: Query Validation**
```python
# Baseado em: LangGraph SQL Query Checking
@node
def validate_query(state: KpiGenerationState):
    """Valida query SQL para erros comuns"""
    validation_prompt = """
    Revise esta query SQL para erros comuns:
    - Sintaxe incorreta
    - Tabelas inexistentes  
    - Colunas inexistentes
    - Performance issues
    - Parâmetros faltando
    """
    
    result = validator_llm.invoke([
        SystemMessage(content=validation_prompt),
        HumanMessage(content=state['generated_query'])
    ])
    
    return {"validation_result": result}
```

#### **Node 4: Query Testing**
```python
# Inspirado em: MCP Database Query Execution
@node
def test_query(state: KpiGenerationState):
    """Testa query contra base de dados real"""
    try:
        # Executa query com dados de teste
        result = execute_query_safely(
            state['generated_query'], 
            {'client_id': 334}  # L2M test client
        )
        
        return {
            "test_result": {
                "success": True,
                "value": result,
                "execution_time": time.time() - start
            }
        }
    except Exception as e:
        return {
            "test_result": {
                "success": False,
                "error": str(e)
            }
        }
```

### **3. Conditional Edges**

```python
def should_retry_generation(state: KpiGenerationState) -> str:
    """Decide se deve regenerar query baseado na validação"""
    if not state['validation_result']['is_valid']:
        return "regenerate"
    elif not state['test_result']['success']:
        return "regenerate"  
    else:
        return "finalize"
```

---

## 🎯 **Implementação por Fases**

### **Fase 1: Setup do Workflow (1-2 dias)**

1. **Criar LangGraph Workflow**
   - Definir `KpiGenerationState`
   - Implementar nodes básicos
   - Configurar conditional edges

2. **Integrar com DataHero Existente**
   - Usar LLM providers existentes
   - Conectar com database utilities
   - Aproveitar sistema de cache

### **Fase 2: Geração dos 6 KPIs Críticos (2-3 dias)**

```python
CRITICAL_KPIS = [
    "total_volume",              # Volume total negociado
    "average_spread",            # Spread médio FX  
    "conversion_rate",           # Taxa de conversão
    "average_ticket",            # Ticket médio
    "retention_rate",            # Taxa de retenção
    "operations_per_analyst"     # Operações por analista
]
```

Para cada KPI:
1. Executar workflow de geração
2. Validar query gerada
3. Testar com dados reais
4. Salvar no JSON atualizado

### **Fase 3: Integração com KpiService (1 dia)**

1. **Modificar KpiService**
   - Ler queries do JSON (não hardcode)
   - Implementar execução dinâmica
   - Manter cache existente

2. **Atualizar Endpoints**
   - Testar `/api/dashboard/kpis`
   - Validar `/api/kpis/available`
   - Verificar performance

### **Fase 4: Sistema On-Demand (1 dia)**

1. **Endpoint de Geração**
   ```python
   @app.post("/api/kpis/generate")
   async def generate_kpi_query(kpi_definition: KpiDefinition):
       """Gera query para novo KPI on-demand"""
       workflow_result = await kpi_generation_workflow.ainvoke({
           "kpi_definition": kpi_definition.dict()
       })
       return workflow_result['final_kpi']
   ```

2. **Interface de Administração**
   - Formulário para novos KPIs
   - Preview de queries geradas
   - Teste antes de salvar

---

## 🔧 **Tecnologias e Padrões**

### **LangGraph Patterns Aplicados**
- ✅ **StateGraph**: Gerenciamento de estado estruturado
- ✅ **Conditional Edges**: Fluxo baseado em validação
- ✅ **Tool Binding**: LLM com ferramentas SQL
- ✅ **Error Handling**: Retry logic robusto

### **MCP Patterns Aplicados**  
- ✅ **Resource Discovery**: Schema como recurso
- ✅ **Tool Execution**: Query execution segura
- ✅ **Structured Output**: Schemas Pydantic
- ✅ **Context Management**: Estado persistente

### **Stack Técnico**
- **LangGraph**: Orchestração do workflow
- **FastAPI**: Endpoints de geração
- **SQLAlchemy**: Execução segura de queries
- **Pydantic**: Validação de schemas
- **PostgreSQL**: Base de dados cliente

---

## 📋 **Critérios de Sucesso**

### **Funcionais**
- [ ] 6 KPIs críticos com queries geradas automaticamente
- [ ] Queries validadas e testadas com dados reais
- [ ] Performance equivalente ou melhor que hardcode
- [ ] Sistema on-demand para novos KPIs

### **Técnicos**
- [ ] Workflow LangGraph funcionando end-to-end
- [ ] Integração com sistema existente sem breaking changes
- [ ] Cobertura de testes para geração de queries
- [ ] Documentação completa do processo

### **Operacionais**
- [ ] Deploy sem downtime
- [ ] Monitoramento de performance
- [ ] Logs estruturados para debugging
- [ ] Rollback plan se necessário

---

## 🚀 **Próximos Passos**

1. **Validar Plano** com stakeholders
2. **Setup Ambiente** de desenvolvimento
3. **Implementar Fase 1** - Workflow básico
4. **Testar com 1 KPI** antes de escalar
5. **Iterar e Refinar** baseado em feedback

---

*Este plano segue as melhores práticas do LangGraph para workflows estruturados e do MCP para descoberta de recursos, garantindo uma solução robusta e escalável.*
