# Node modules
node_modules
apps/frontend/node_modules
apps/backend/.venv

# Build artifacts
dist
build
.next
apps/frontend/dist
apps/frontend/build

# Turbo cache
.turbo

# Environment files
.env
.env.local
.env.development
.env.test
apps/frontend/.env
apps/frontend/.env.local
apps/backend/.env

# Git
.git
.gitignore

# Documentation
README.md
docs/
*.md

# IDE
.vscode
.idea

# OS
.DS_Store
Thumbs.db

# Testing
coverage/
.nyc_output
apps/frontend/coverage

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Cache
.npm
.yarn-integrity

# Python cache
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/

# Poetry
poetry.lock

# Deployment files (not needed in container)
render.yaml
railway.toml
Procfile